# Endpoint và Request Body của <PERSON>u <PERSON>ho (ton-kho/phieu-nhap-kho)

## 📍 **ENDPOINTS CHÍNH XÁC**

### **Base URL:**

```
/api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/
```

### **🔗 Tất cả các endpoints:**

#### **1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Phiếu nhập kho chính):**

```
GET    /api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/
POST   /api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/
GET    /api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/{uuid}/
PUT    /api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/{uuid}/
DELETE /api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/{uuid}/
```

#### **2. ChiTietPhieuNhapKho (Chi tiết phiếu nhập kho):**

```
GET    /api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/{phieu_nhap_kho_pk}/chi-tiet/
POST   /api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/{phieu_nhap_kho_pk}/chi-tiet/
GET    /api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/{phieu_nhap_kho_pk}/chi-tiet/{uuid}/
PUT    /api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/{phieu_nhap_kho_pk}/chi-tiet/{uuid}/
DELETE /api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/{phieu_nhap_kho_pk}/chi-tiet/{uuid}/
```

## 🎯 **PHƯƠNG THỨC HTTP HỖ TRỢ**

### **PhieuNhapKho:**

- ✅ **GET** - Lấy danh sách và chi tiết
- ✅ **POST** - Tạo mới
- ✅ **PUT** - Cập nhật (full update)
- ❌ **PATCH** - Không hỗ trợ
- ✅ **DELETE** - Xóa

### **ChiTietPhieuNhapKho:**

- ✅ **GET** - Lấy danh sách và chi tiết
- ✅ **POST** - Tạo mới
- ✅ **PUT** - Cập nhật (full update)
- ❌ **PATCH** - Không hỗ trợ
- ✅ **DELETE** - Xóa

## 📋 **REQUEST BODY STRUCTURE - PHIẾU NHẬP KHO**

### **🔑 Cấu trúc đầy đủ (PhieuNhapKhoModel):**

```json
{
    // ===== THÔNG TIN CƠ BẢN =====
    "action": "string",              // Hành động (max 10 chars) - REQUIRED
    "question_ids": "string",        // ID câu hỏi (max 255 chars)
    "ma_ngv": "string",              // Mã người giao việc (max 10 chars) - REQUIRED
    "ma_gd": "string",               // Mã giao dịch (max 10 chars) - REQUIRED
    "ma_kh": "uuid",                 // Mã khách hàng (Foreign Key)
    "dien_giai": "string",           // Diễn giải (TextField) - REQUIRED

    // ===== THÔNG TIN ĐƠN VỊ VÀ TIẾN ĐỘ =====
    "unit_id": "integer",            // ID đơn vị - REQUIRED
    "id_progress": "integer",        // ID tiến độ - REQUIRED
    "xprogress": "string",           // Chi tiết tiến độ (max 255 chars)

    // ===== THÔNG TIN CHỨNG TỪ =====
    "i_so_ct": "string",             // Số chứng từ nội bộ (max 10 chars) - REQUIRED
    "ma_nk": "uuid",                 // Mã nhập kho (Foreign Key)
    "so_ct": "uuid",                 // Số chứng từ (Foreign Key)
    "ngay_ct": "date",               // Ngày chứng từ (YYYY-MM-DD) - REQUIRED
    "ngay_lct": "date",              // Ngày lập chứng từ (YYYY-MM-DD) - REQUIRED
    "xdatetime2": "string",          // Thông tin thời gian bổ sung (max 255 chars)

    // ===== THÔNG TIN TIỀN TỆ =====
    "ma_nt": "uuid",                 // Mã ngoại tệ (Foreign Key)
    "ty_gia": "decimal",             // Tỷ giá (15 digits, 2 decimal places) - REQUIRED

    // ===== TRẠNG THÁI =====
    "status": "string",              // Trạng thái (max 2 chars) - REQUIRED
    "transfer_yn": "boolean",        // Đã chuyển (default: false)

    // ===== THÔNG TIN SỐ LƯỢNG VÀ TIỀN =====
    "t_so_luong": "decimal",         // Tổng số lượng (15 digits, 2 decimal places) - REQUIRED
    "t_tien_nt": "decimal",          // Tổng tiền ngoại tệ (15 digits, 2 decimal places) - REQUIRED
    "t_tien": "decimal",             // Tổng tiền (15 digits, 2 decimal places) - REQUIRED

    // ===== THÔNG TIN KHÁC =====
    "xfile": "string",               // Tham chiếu tệp (max 255 chars)

    // ===== NESTED DATA =====
    "chi_tiet": [...]                // Chi tiết phiếu nhập kho (array)
}
```

## 📝 **REQUEST BODY STRUCTURE - CHI TIẾT PHIẾU NHẬP KHO**

### **🔑 Cấu trúc đầy đủ (ChiTietPhieuNhapKhoModel):**

```json
{
  "chi_tiet": [
    {
      // ===== THÔNG TIN DÒNG =====
      "line": "integer", // Số thứ tự dòng - REQUIRED

      // ===== THÔNG TIN VẬT TƯ =====
      "ma_vt": "uuid", // Mã vật tư (Foreign Key)
      "dvt": "uuid", // Đơn vị tính (Foreign Key)
      "ten_dvt": "string", // Tên đơn vị tính (max 50 chars) - REQUIRED

      // ===== THÔNG TIN KHO =====
      "ma_kho": "uuid", // Mã kho (Foreign Key)
      "ten_kho": "string", // Tên kho (max 100 chars) - REQUIRED

      // ===== THÔNG TIN LÔ =====
      "ma_lo": "string", // Mã lô (max 20 chars)
      "ten_lo": "string", // Tên lô (max 100 chars)
      "lo_yn": "integer", // Flag lô - REQUIRED

      // ===== THÔNG TIN VỊ TRÍ =====
      "ma_vi_tri": "string", // Mã vị trí (max 20 chars)
      "ten_vi_tri": "string", // Tên vị trí (max 100 chars)
      "vi_tri_yn": "integer", // Flag vị trí - REQUIRED

      // ===== THÔNG TIN SỐ LƯỢNG VÀ GIÁ =====
      "he_so": "decimal", // Hệ số (15 digits, 2 decimal places) - REQUIRED
      "qc_yn": "integer", // Flag QC - REQUIRED
      "so_luong": "decimal", // Số lượng (15 digits, 2 decimal places) - REQUIRED
      "pn_tb": "integer", // Phiếu nhập trung bình - REQUIRED
      "gia_nt": "decimal", // Giá ngoại tệ (15 digits, 2 decimal places) - REQUIRED
      "tien_nt": "decimal", // Tiền ngoại tệ (15 digits, 2 decimal places) - REQUIRED
      "gia": "decimal", // Giá (15 digits, 2 decimal places) - REQUIRED
      "tien": "decimal", // Tiền (15 digits, 2 decimal places) - REQUIRED

      // ===== THÔNG TIN TÀI KHOẢN =====
      "tk_vt": "uuid", // Tài khoản vật tư (Foreign Key)
      "ma_nx": "uuid", // Mã nhập xuất (Foreign Key)
      "tk_du": "uuid", // Tài khoản đích (Foreign Key)

      // ===== THÔNG TIN TỔ CHỨC =====
      "ma_bp": "uuid", // Mã bộ phận (Foreign Key)
      "ma_vv": "uuid", // Mã vụ việc (Foreign Key)
      "ma_hd": "uuid", // Mã hợp đồng (Foreign Key)
      "ma_dtt": "uuid", // Mã đợt thanh toán (Foreign Key)
      "ma_ku": "uuid", // Mã khế ước (Foreign Key)
      "ma_phi": "uuid", // Mã phí (Foreign Key)
      "ma_sp": "uuid", // Mã sản phẩm (Foreign Key)
      "ma_lsx": "string", // Mã lệnh sản xuất (max 20 chars)
      "ma_cp0": "uuid", // Mã chi phí (Foreign Key)

      // ===== THÔNG TIN PHIẾU NHẬP =====
      "sl_pn": "decimal", // Số lượng phiếu nhập (15 digits, 2 decimal places) - REQUIRED
      "id_pn": "integer", // ID phiếu nhập - REQUIRED
      "line_pn": "integer", // Số dòng phiếu nhập - REQUIRED

      // ===== THÔNG TIN SẢN XUẤT =====
      "id_sx1": "integer", // ID sản xuất 1 - REQUIRED
      "line_sx1": "integer" // Số dòng sản xuất 1 - REQUIRED
    }
  ]
}
```

## 🎯 **VÍ DỤ REQUEST BODY HOÀN CHỈNH**

### **POST - Tạo Phiếu Nhập Kho mới:**

```json
{
  // ===== THÔNG TIN CƠ BẢN =====
  "action": "NHAP_KHO", 
  "question_ids": "Q001,Q002",
  "ma_ngv": "3",
  "ma_gd": "DC", //Giao dịch
  "ma_kh": "550e8400-e29b-41d4-a716-************", //Mã đối tượng (uuid)
  "dien_giai": "Nhập kho nguyên liệu tháng 12/2024", //Diễn giải

  // ===== THÔNG TIN ĐƠN VỊ VÀ TIẾN ĐỘ =====
  "unit_id": 1, //id đơn vị
  "id_progress": 1, //id tiến độ (backend auto generate)
  "xprogress": "Đang xử lý nhập kho", //Chi tiết tiến độ (backend auto generate)

  // ===== THÔNG TIN CHỨNG TỪ =====
  "i_so_ct": "NK001", //backend xử lý
  "ma_nk": "550e8400-e29b-41d4-a716-************", //mã quyển chứng từ (uuid)
  "so_ct": "550e8400-e29b-41d4-a716-************", //backend auto generate
  "ngay_ct": "2024-01-15", // Ngày chứng từ
  "ngay_lct": "2024-01-15", // Ngày lập chứng từ = ngày chứng từ
  "xdatetime2": "2024-01-15T10:30:00", // backend auto generate

  // ===== THÔNG TIN TIỀN TỆ =====
  "ma_nt": "550e8400-e29b-41d4-a716-************", //Mã ngoại tệ (uuid)
  "ty_gia": 1.0, //Tỷ giá

  // ===== TRẠNG THÁI =====
  "status": "1", // Trạng thái (1: Hoàn thành, 0: Chưa hoàn thành)
  "transfer_yn": false,

  // ===== THÔNG TIN SỐ LƯỢNG VÀ TIỀN =====
  "t_so_luong": 100.0,
  "t_tien_nt": 5000000.0,
  "t_tien": 5000000.0,

  // ===== THÔNG TIN KHÁC =====
  "xfile": "phieu_nhap_kho_001.pdf",

  // ===== CHI TIẾT PHIẾU NHẬP KHO =====
  "chi_tiet": [
    {
      "line": 1,
      "ma_vt": "550e8400-e29b-41d4-a716-************",
      "dvt": "550e8400-e29b-41d4-a716-************", 
      "ten_dvt": "Kg",
      "ma_kho": "550e8400-e29b-41d4-a716-************", 
      "ten_kho": "Kho nguyên liệu", 
      "ma_lo": "LO001", 
      "ten_lo": "Lô nguyên liệu 001",
      "lo_yn": 1,
      "ma_vi_tri": "A01",
      "ten_vi_tri": "Kệ A - Tầng 1",
      "vi_tri_yn": 1,
      "he_so": 1.0,
      "qc_yn": 1,
      "so_luong": 50.0,
      "pn_tb": 1,
      "gia_nt": 100000.0,
      "tien_nt": 5000000.0,
      "gia": 100000.0,
      "tien": 5000000.0,
      "tk_vt": "550e8400-e29b-41d4-a716-446655440008",
      "ma_nx": "550e8400-e29b-41d4-a716-446655440009",
      "tk_du": "550e8400-e29b-41d4-a716-446655440010",
      "ma_bp": "550e8400-e29b-41d4-a716-446655440011",
      "ma_vv": "550e8400-e29b-41d4-a716-446655440012",
      "ma_hd": "550e8400-e29b-41d4-a716-446655440013",
      "ma_dtt": "550e8400-e29b-41d4-a716-446655440014",
      "ma_ku": "550e8400-e29b-41d4-a716-446655440015",
      "ma_phi": "550e8400-e29b-41d4-a716-446655440016",
      "ma_sp": "550e8400-e29b-41d4-a716-446655440017",
      "ma_lsx": "LSX001",
      "ma_cp0": "550e8400-e29b-41d4-a716-446655440018",
      "sl_pn": 50.0,
      "id_pn": 1,
      "line_pn": 1,
      "id_sx1": 1,
      "line_sx1": 1
    },
    {
      "line": 2,
      "ma_vt": "550e8400-e29b-41d4-a716-446655440019",
      "dvt": "550e8400-e29b-41d4-a716-446655440020",
      "ten_dvt": "Thùng",
      "ma_kho": "550e8400-e29b-41d4-a716-************",
      "ten_kho": "Kho nguyên liệu",
      "ma_lo": "LO002",
      "ten_lo": "Lô nguyên liệu 002",
      "lo_yn": 1,
      "ma_vi_tri": "A02",
      "ten_vi_tri": "Kệ A - Tầng 2",
      "vi_tri_yn": 1,
      "he_so": 1.0,
      "qc_yn": 1,
      "so_luong": 50.0,
      "pn_tb": 1,
      "gia_nt": 0.0,
      "tien_nt": 0.0,
      "gia": 0.0,
      "tien": 0.0,
      "tk_vt": "550e8400-e29b-41d4-a716-446655440021",
      "ma_nx": "550e8400-e29b-41d4-a716-446655440022",
      "tk_du": "550e8400-e29b-41d4-a716-446655440023",
      "ma_bp": "550e8400-e29b-41d4-a716-446655440011",
      "ma_vv": "550e8400-e29b-41d4-a716-446655440012",
      "ma_hd": "550e8400-e29b-41d4-a716-446655440013",
      "ma_dtt": "550e8400-e29b-41d4-a716-446655440014",
      "ma_ku": "550e8400-e29b-41d4-a716-446655440015",
      "ma_phi": "550e8400-e29b-41d4-a716-446655440016",
      "ma_sp": "550e8400-e29b-41d4-a716-446655440024",
      "ma_lsx": "LSX002",
      "ma_cp0": "550e8400-e29b-41d4-a716-446655440025",
      "sl_pn": 50.0,
      "id_pn": 2,
      "line_pn": 2,
      "id_sx1": 2,
      "line_sx1": 2
    }
  ]
}
```

## ⚠️ **TRƯỜNG BẮT BUỘC (REQUIRED)**

### **PhieuNhapKho chính:**

#### **Thông tin cơ bản:**

- `action` - Hành động (max 10 chars)
- `ma_ngv` - Mã người giao việc (max 10 chars)
- `ma_gd` - Mã giao dịch (max 10 chars)
- `dien_giai` - Diễn giải (TextField)

#### **Thông tin đơn vị:**

- `unit_id` - ID đơn vị (integer)
- `id_progress` - ID tiến độ (integer)

#### **Thông tin chứng từ:**

- `i_so_ct` - Số chứng từ nội bộ (max 10 chars)
- `ngay_ct` - Ngày chứng từ
- `ngay_lct` - Ngày lập chứng từ

#### **Thông tin tiền tệ:**

- `ty_gia` - Tỷ giá (15 digits, 2 decimal places)

#### **Trạng thái:**

- `status` - Trạng thái (max 2 chars)

#### **Thông tin số lượng:**

- `t_so_luong` - Tổng số lượng (15 digits, 2 decimal places)
- `t_tien_nt` - Tổng tiền ngoại tệ (15 digits, 2 decimal places)
- `t_tien` - Tổng tiền (15 digits, 2 decimal places)

### **ChiTietPhieuNhapKho:**

#### **Thông tin cơ bản:**

- `line` - Số thứ tự dòng (integer)
- `ten_dvt` - Tên đơn vị tính (max 50 chars)
- `ten_kho` - Tên kho (max 100 chars)

#### **Flags và hệ số:**

- `lo_yn` - Flag lô (integer)
- `vi_tri_yn` - Flag vị trí (integer)
- `he_so` - Hệ số (15 digits, 2 decimal places)
- `qc_yn` - Flag QC (integer)
- `pn_tb` - Phiếu nhập trung bình (integer)

#### **Thông tin số lượng và giá:**

- `so_luong` - Số lượng (15 digits, 2 decimal places)
- `gia_nt` - Giá ngoại tệ (15 digits, 2 decimal places)
- `tien_nt` - Tiền ngoại tệ (15 digits, 2 decimal places)
- `gia` - Giá (15 digits, 2 decimal places)
- `tien` - Tiền (15 digits, 2 decimal places)

#### **Thông tin phiếu nhập:**

- `sl_pn` - Số lượng phiếu nhập (15 digits, 2 decimal places)
- `id_pn` - ID phiếu nhập (integer)
- `line_pn` - Số dòng phiếu nhập (integer)

#### **Thông tin sản xuất:**

- `id_sx1` - ID sản xuất 1 (integer)
- `line_sx1` - Số dòng sản xuất 1 (integer)

## 🚀 **JAVASCRIPT IMPLEMENTATION**

### **Tạo Phiếu Nhập Kho mới:**

```javascript
async function createPhieuNhapKho(entitySlug, data) {
  try {
    const response = await fetch(
      `/api/entities/${entitySlug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/`,
      {
        method: "POST",
        headers: {
          Authorization: `Token ${localStorage.getItem("authToken")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        `HTTP error! status: ${response.status}, details: ${JSON.stringify(
          errorData
        )}`
      );
    }

    const result = await response.json();
    console.log("Phiếu nhập kho created successfully:", result);
    return result;
  } catch (error) {
    console.error("Error creating phiếu nhập kho:", error);
    throw error;
  }
}

// Sử dụng
const newPhieuNhapKho = {
  action: "NHAP_KHO",
  ma_ngv: "NV001",
  ma_gd: "GD001",
  dien_giai: "Nhập kho nguyên liệu",
  unit_id: 1,
  id_progress: 1,
  i_so_ct: "NK001",
  ngay_ct: "2024-01-15",
  ngay_lct: "2024-01-15",
  ty_gia: 1.0,
  status: "1",
  t_so_luong: 100.0,
  t_tien_nt: 5000000.0,
  t_tien: 5000000.0,
  chi_tiet: [
    {
      line: 1,
      ten_dvt: "Kg",
      ten_kho: "Kho nguyên liệu",
      lo_yn: 1,
      vi_tri_yn: 1,
      he_so: 1.0,
      qc_yn: 1,
      so_luong: 50.0,
      pn_tb: 1,
      gia_nt: 100000.0,
      tien_nt: 5000000.0,
      gia: 100000.0,
      tien: 5000000.0,
      sl_pn: 50.0,
      id_pn: 1,
      line_pn: 1,
      id_sx1: 1,
      line_sx1: 1,
    },
  ],
};

createPhieuNhapKho("my-company", newPhieuNhapKho);
```

### **Lấy danh sách Phiếu Nhập Kho:**

```javascript
async function getPhieuNhapKhoList(entitySlug, filters = {}) {
  const queryParams = new URLSearchParams();

  // Thêm các filter parameters
  if (filters.search) queryParams.append("search", filters.search);
  if (filters.status) queryParams.append("status", filters.status);
  if (filters.from_date) queryParams.append("from_date", filters.from_date);
  if (filters.to_date) queryParams.append("to_date", filters.to_date);
  if (filters.page) queryParams.append("page", filters.page);
  if (filters.page_size) queryParams.append("page_size", filters.page_size);

  try {
    const response = await fetch(
      `/api/entities/${entitySlug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/?${queryParams}`,
      {
        headers: {
          Authorization: `Token ${localStorage.getItem("authToken")}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error fetching phiếu nhập kho list:", error);
    throw error;
  }
}
```

### **Cập nhật Phiếu Nhập Kho:**

```javascript
async function updatePhieuNhapKho(entitySlug, uuid, data) {
  try {
    const response = await fetch(
      `/api/entities/${entitySlug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/${uuid}/`,
      {
        method: "PUT",
        headers: {
          Authorization: `Token ${localStorage.getItem("authToken")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        `HTTP error! status: ${response.status}, details: ${JSON.stringify(
          errorData
        )}`
      );
    }

    const result = await response.json();
    console.log("Phiếu nhập kho updated successfully:", result);
    return result;
  } catch (error) {
    console.error("Error updating phiếu nhập kho:", error);
    throw error;
  }
}
```

## 📊 **RESPONSE FORMAT**

### **Success Response (201 Created / 200 OK):**

```json
{
  "uuid": "550e8400-e29b-41d4-a716-************",
  "entity_model": "entity-uuid",
  "action": "NHAP_KHO",
  "question_ids": "Q001,Q002",
  "ma_ngv": "NV001",
  "ma_gd": "GD001",
  "ma_kh": "customer-uuid",
  "ma_kh_data": {
    "uuid": "customer-uuid",
    "customer_name": "Công ty ABC",
    "ma_so_thue": "0123456789"
  },
  "dien_giai": "Nhập kho nguyên liệu tháng 12/2024",
  "unit_id": 1,
  "id_progress": 1,
  "xprogress": "Đang xử lý nhập kho",
  "i_so_ct": "NK001",
  "ma_nk": "document-permission-uuid",
  "ma_nk_data": {
    "uuid": "document-permission-uuid",
    "ma_nk": "NK",
    "ten_nk": "Nhập kho"
  },
  "so_ct": "document-uuid",
  "so_ct_data": {
    "uuid": "document-uuid",
    "so_ct": "NK001"
  },
  "ngay_ct": "2024-01-15",
  "ngay_lct": "2024-01-15",
  "xdatetime2": "2024-01-15T10:30:00",
  "ma_nt": "currency-uuid",
  "ma_nt_data": {
    "uuid": "currency-uuid",
    "code": "VND",
    "name": "Việt Nam Đồng"
  },
  "ty_gia": 1.0,
  "status": "1",
  "transfer_yn": false,
  "t_so_luong": 100.0,
  "t_tien_nt": 5000000.0,
  "t_tien": 5000000.0,
  "xfile": "phieu_nhap_kho_001.pdf",
  "chi_tiet": [
    {
      "uuid": "detail-uuid-1",
      "phieu_nhap_kho": "phieu-nhap-kho-uuid",
      "line": 1,
      "ma_vt": "material-uuid",
      "dvt": "unit-uuid",
      "ten_dvt": "Kg",
      "ma_kho": "warehouse-uuid",
      "ten_kho": "Kho nguyên liệu",
      "ma_lo": "LO001",
      "ten_lo": "Lô nguyên liệu 001",
      "lo_yn": 1,
      "ma_vi_tri": "A01",
      "ten_vi_tri": "Kệ A - Tầng 1",
      "vi_tri_yn": 1,
      "he_so": 1.0,
      "qc_yn": 1,
      "so_luong": 50.0,
      "pn_tb": 1,
      "gia_nt": 100000.0,
      "tien_nt": 5000000.0,
      "gia": 100000.0,
      "tien": 5000000.0,
      "sl_pn": 50.0,
      "id_pn": 1,
      "line_pn": 1,
      "id_sx1": 1,
      "line_sx1": 1,
      "created": "2024-01-15T10:30:00Z",
      "updated": "2024-01-15T10:30:00Z"
    }
  ],
  "created": "2024-01-15T10:30:00Z",
  "updated": "2024-01-15T10:30:00Z"
}
```

## ✅ **KẾT LUẬN**

**Phiếu Nhập Kho** có cấu trúc phức tạp với:

- ✅ **5 phương thức HTTP** cho PhieuNhapKho (GET, POST, PUT, DELETE)
- ✅ **5 phương thức HTTP** cho ChiTietPhieuNhapKho (GET, POST, PUT, DELETE)
- ✅ **Nested creation** - Tạo phiếu nhập + chi tiết cùng lúc
- ✅ **Rich data model** - 20+ fields cho phiếu chính, 40+ fields cho chi tiết
- ✅ **Complex relationships** - Liên kết với 15+ models khác
- ✅ **Warehouse management** - Quản lý kho, lô, vị trí chi tiết
- ✅ **Multi-currency support** - Hỗ trợ ngoại tệ và tỷ giá
- ✅ **Production integration** - Tích hợp với sản xuất và lệnh sản xuất
- ✅ **Quality control** - Hỗ trợ kiểm soát chất lượng
- ✅ **Authentication required** - Token authentication cho tất cả endpoints
