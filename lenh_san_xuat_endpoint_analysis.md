# Phân Tích Endpoint Lệnh Sản Xuất (Production Order)

## 🔍 **TÌNH TRẠNG HIỆN TẠI**

### ❌ **KHÔNG CÓ ENDPOINT TRỰC TIẾP CHO LỆNH SẢN XUẤT**

Sau khi tìm kiếm toàn bộ codebase, **không tìm thấy**:
- ✗ Model `LenhSanXuatModel` 
- ✗ ViewSet cho lệnh sản xuất
- ✗ URL routing cho `lenh-san-xuat`
- ✗ API endpoint GET cho production orders

### 📋 **THÔNG TIN HIỆN CÓ**

#### **1. Mã lệnh sản xuất (`ma_lsx`) được lưu dưới dạng CharField:**

```python
# Trong các model khác:
ma_lsx = models.CharField(
    max_length=20,
    blank=True,
    verbose_name=_('Mã lệnh sản xuất'),
    help_text=_('Production order code'),
)
```

#### **2. Các model có chứa `ma_lsx`:**
- `ChiTietPhieuNhapKhoModel`
- `ChiTietPhieuXuatKhoModel` 
- `ChiTietPhieuYeuCauXuatKhoModel`
- `GiaThanhCacBuocModel`
- `KhaiBaoDoiTuongNhanPhanBoChiPhiModel`
- Các model trong mua_hang và ban_hang

## 🎯 **CÁC ENDPOINT CÓ THỂ LIÊN QUAN**

### **1. GiaThanhCacBuoc (Cost Calculation Steps)**

**Endpoint:**
```
GET/POST /api/entities/{entity_slug}/erp/gia-thanh/tinh-gia-thanh/gia-thanh-cac-buoc/
```

**Có thể chứa thông tin:**
- `ma_lsx` - Mã lệnh sản xuất
- `ma_sp` - Mã sản phẩm
- `sl_tp` - Số lượng thành phẩm
- `gia` - Giá thành
- `tien` - Thành tiền

### **2. TheGiaThanhSanPham (Product Cost Card Report)**

**Endpoint:**
```
POST /api/entities/{entity_slug}/erp/gia-thanh/bao-cao/the-gia-thanh-san-pham/
```

**Request Body:**
```json
{
    "tu_ky": 1,
    "tu_nam": 2024,
    "den_ky": 12,
    "den_nam": 2024,
    "ma_sp": "SP001",
    "ma_lsx": "LSX001"
}
```

**Response có thể chứa:**
- Danh sách lệnh sản xuất theo sản phẩm
- Chi phí theo lệnh sản xuất
- Thông tin giá thành

### **3. Phiếu Nhập/Xuất Kho**

**Endpoints:**
```
GET /api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/
GET /api/entities/{entity_slug}/erp/ton-kho/xuat-kho-noi-bo/phieu-xuat-kho/
```

**Có thể filter theo ma_lsx:**
```
GET /api/entities/{entity_slug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/?ma_lsx=LSX001
```

## 💡 **GIẢI PHÁP THAY THẾ**

### **1. Lấy danh sách lệnh sản xuất từ GiaThanhCacBuoc:**

```javascript
async function getProductionOrders(entitySlug, filters = {}) {
    try {
        const queryParams = new URLSearchParams();
        
        // Thêm filters
        if (filters.ky) queryParams.append('ky', filters.ky);
        if (filters.nam) queryParams.append('nam', filters.nam);
        if (filters.ma_sp) queryParams.append('ma_sp', filters.ma_sp);
        
        const response = await fetch(
            `/api/entities/${entitySlug}/erp/gia-thanh/tinh-gia-thanh/gia-thanh-cac-buoc/?${queryParams}`,
            {
                headers: {
                    'Authorization': `Token ${localStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json',
                }
            }
        );

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        
        // Extract unique production orders
        const productionOrders = result.results
            .filter(item => item.ma_lsx)
            .reduce((acc, item) => {
                if (!acc.find(po => po.ma_lsx === item.ma_lsx)) {
                    acc.push({
                        ma_lsx: item.ma_lsx,
                        ma_sp: item.ma_sp,
                        ten_sp: item.ma_sp_data?.ten_vt || '',
                        sl_tp: item.sl_tp,
                        gia: item.gia,
                        tien: item.tien,
                        ky: item.ky,
                        nam: item.nam
                    });
                }
                return acc;
            }, []);
            
        return productionOrders;
    } catch (error) {
        console.error('Error fetching production orders:', error);
        throw error;
    }
}
```

### **2. Lấy chi tiết lệnh sản xuất từ báo cáo giá thành:**

```javascript
async function getProductionOrderDetails(entitySlug, ma_lsx, period) {
    try {
        const requestBody = {
            tu_ky: period.month,
            tu_nam: period.year,
            den_ky: period.month,
            den_nam: period.year,
            ma_lsx: ma_lsx
        };

        const response = await fetch(
            `/api/entities/${entitySlug}/erp/gia-thanh/bao-cao/the-gia-thanh-san-pham/`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Token ${localStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            }
        );

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        return result;
    } catch (error) {
        console.error('Error fetching production order details:', error);
        throw error;
    }
}
```

### **3. Tìm kiếm lệnh sản xuất trong phiếu nhập/xuất kho:**

```javascript
async function searchProductionOrdersInWarehouse(entitySlug, ma_lsx) {
    try {
        // Tìm trong phiếu nhập kho
        const nhapKhoResponse = await fetch(
            `/api/entities/${entitySlug}/erp/ton-kho/nhap-kho-noi-bo/phieu-nhap-kho/?search=${ma_lsx}`,
            {
                headers: {
                    'Authorization': `Token ${localStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json',
                }
            }
        );

        // Tìm trong phiếu xuất kho
        const xuatKhoResponse = await fetch(
            `/api/entities/${entitySlug}/erp/ton-kho/xuat-kho-noi-bo/phieu-xuat-kho/?search=${ma_lsx}`,
            {
                headers: {
                    'Authorization': `Token ${localStorage.getItem('authToken')}`,
                    'Content-Type': 'application/json',
                }
            }
        );

        const nhapKhoData = await nhapKhoResponse.json();
        const xuatKhoData = await xuatKhoResponse.json();

        return {
            nhap_kho: nhapKhoData.results || [],
            xuat_kho: xuatKhoData.results || []
        };
    } catch (error) {
        console.error('Error searching production orders in warehouse:', error);
        throw error;
    }
}
```

## 📊 **VÍ DỤ SỬ DỤNG**

### **Lấy danh sách lệnh sản xuất tháng hiện tại:**

```javascript
const currentDate = new Date();
const productionOrders = await getProductionOrders('my-company', {
    ky: currentDate.getMonth() + 1,
    nam: currentDate.getFullYear()
});

console.log('Production Orders:', productionOrders);
```

### **Lấy chi tiết lệnh sản xuất cụ thể:**

```javascript
const orderDetails = await getProductionOrderDetails('my-company', 'LSX001', {
    month: 12,
    year: 2024
});

console.log('Production Order Details:', orderDetails);
```

## ⚠️ **LƯU Ý QUAN TRỌNG**

1. **Không có model riêng**: `ma_lsx` chỉ là CharField trong các model khác
2. **Không có CRUD operations**: Không thể tạo/sửa/xóa lệnh sản xuất trực tiếp
3. **Dữ liệu phân tán**: Thông tin lệnh sản xuất nằm rải rác trong nhiều model
4. **Phụ thuộc vào giá thành**: Chủ yếu thông qua module gia_thanh

## 🔮 **KHUYẾN NGHỊ**

Để có endpoint GET lệnh sản xuất hoàn chỉnh, cần:

1. **Tạo model LenhSanXuatModel** riêng biệt
2. **Tạo ViewSet và URL routing** cho lệnh sản xuất  
3. **Migrate dữ liệu** từ các CharField hiện tại
4. **Cập nhật relationships** trong các model liên quan

Hiện tại, sử dụng các giải pháp thay thế ở trên để lấy thông tin lệnh sản xuất.
